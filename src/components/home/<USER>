import Link from 'next/link';
import { FaPhone, FaWhatsapp, FaMapMarkerAlt, FaClock } from 'react-icons/fa';

const ContactSection = () => {
  return (
    <section className="section">
      <div className="container">
        <h2 className="section-title">Contact & Visit Us</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-2xl font-bold mb-6">Get In Touch</h3>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <FaMapMarkerAlt className="text-blue-600 text-2xl mt-1 mr-4" />
                <div>
                  <h4 className="font-bold mb-1">Our Location</h4>
                  <p>Shop No.1,Kediya Vyapar Bihar, Ambedkar Chowk, Baloda Bazar, Raipur, 493332</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <FaPhone className="text-blue-600 text-2xl mt-1 mr-4" />
                <div>
                  <h4 className="font-bold mb-1">Call Us</h4>
                  <p>
                    <a href="tel:+919926480250" className="hover:text-blue-600 transition-colors">
                      +91 99264 80250
                    </a>
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <FaWhatsapp className="text-blue-600 text-2xl mt-1 mr-4" />
                <div>
                  <h4 className="font-bold mb-1">WhatsApp</h4>
                  <p>
                    <a href="https://wa.me/9926480250" className="hover:text-blue-600 transition-colors">
                      Message us on WhatsApp
                    </a>
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <FaClock className="text-blue-600 text-2xl mt-1 mr-4" />
                <div>
                  <h4 className="font-bold mb-1">Business Hours</h4>
                  <p>Saturday-Thursday: 10:00 AM - 9:00 PM</p>
                  <p>Friday: Closed</p>
                </div>
              </div>
            </div>
            
            <div className="mt-8">
              <h4 className="font-bold text-xl mb-2">Have a query?</h4>
              <p className="mb-4">Just give us a call or drop in!</p>
              <Link href="/contact" className="btn-primary inline-block">
                Contact Us
              </Link>
            </div>
          </div>
          
          <div className="h-[400px] rounded-lg overflow-hidden shadow-lg relative">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3719.8!2d81.3498!3d21.6637!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjHCsDM5JzQ5LjMiTiA4McKwMjAnNTkuMyJF!5e1!3m2!1sen!2sin!4v1638461460188!5m2!1sen!2sin&maptype=roadmap&zoom=16&q=Baloda+Bazar+Raipur+Chhattisgarh"
              width="100%"
              height="100%"
              style={{ border: 0, filter: 'contrast(1.1) saturate(1.2)' }}
              allowFullScreen
              loading="lazy"
              title="RISHABH ELECTRONICS Store Location - Baloda Bazar, Raipur"
            ></iframe>

            {/* Overlay with store info */}
            <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-gray-200">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <div>
                  <h4 className="font-bold text-sm text-gray-800">RISHABH ELECTRONICS</h4>
                  <p className="text-xs text-gray-600">Baloda Bazar, Raipur</p>
                </div>
              </div>
            </div>

            {/* Directions button */}
            <div className="absolute bottom-4 right-4">
              <a
                href="https://maps.google.com/maps?q=Shop+No.1+Kediya+Vyapar+Bihar+Ambedkar+Chowk+Baloda+Bazar+Raipur+493332"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                🗺️ Get Directions
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;